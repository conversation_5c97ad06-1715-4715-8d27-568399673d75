# 使用 Node.js 18 的 Alpine 官方镜像（轻量级）
FROM node:20-bullseye

# 设置工作目录
WORKDIR /app

# 替换 Alpine 源为阿里云镜像（加速国内下载）
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list && \
  sed -i 's/security.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list

# 安装编译依赖和图像处理工具
RUN apt-get update && apt-get install -y \
  build-essential \
  autoconf \
  automake \
  libtool \
  nasm \
  optipng \
  # 添加 libc6 兼容库
  libc6-dev

# 安装 optipng 的 Node.js 封装（用于构建流程中的图片压缩）
RUN yarn add optipng-bin

# 安装 Taro 4.0.12 CLI（全局安装）
RUN yarn global add @tarojs/cli@4.0.12

# 重建 SWC 二进制绑定
RUN npm rebuild @swc/core --update-binary -g
