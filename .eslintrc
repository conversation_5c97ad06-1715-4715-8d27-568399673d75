{"root": true, "env": {"node": true, "es2021": true, "browser": true}, "extends": ["plugin:vue/vue3-recommended", "eslint-config-taro/vue3", "plugin:prettier/recommended"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "rules": {"vue/multi-word-component-names": "off", "no-console": ["warn", {"allow": ["warn", "error"]}], "prettier/prettier": ["error", {"semi": false, "singleQuote": true, "printWidth": 100, "trailingComma": "none", "htmlWhitespaceSensitivity": "ignore", "vueIndentScriptAndStyle": false}], "vue/max-attributes-per-line": "off", "vue/singleline-html-element-content-newline": "off", "vue/html-self-closing": ["error", {"html": {"void": "always", "normal": "never", "component": "always"}, "svg": "always", "math": "always"}]}, "overrides": [{"files": ["*.vue"], "rules": {"indent": "off"}}]}