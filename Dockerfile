# 定制的镜像都是基于 FROM 的镜像
FROM registry.cn-shanghai.aliyuncs.com/chos/base-node-14.18.1-alpine:1.0.1

# 暴露8036端口
EXPOSE 8036

# 如果default.conf 存在则删除
RUN if [ -f /etc/nginx/http.d/default.conf ]; then rm /etc/nginx/http.d/default.conf; fi
RUN ls /etc/nginx/http.d
# 写入nginx配置文件
COPY nginx.conf /etc/nginx/http.d/nginx.conf

# 设置工作路径
ENV APP_PATH /opt/app
WORKDIR ${APP_PATH}

# 添加dist目录到工作路径
ADD ./dist ${APP_PATH}/dist

# 创建/var/www/frontend并拷贝dist目录中文件到/var/www/frontend
RUN mkdir -p /var/www/frontend && \
  cp -r ${APP_PATH}/dist/* /var/www/frontend

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
