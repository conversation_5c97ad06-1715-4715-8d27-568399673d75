<!-- 首页 -->
<template>
  <van-nav-bar title="膳食画像" fixed>
    <!-- <template #right>
      <van-icon name="edit" size="20" @click="editUserInfo" />
    </template> -->
  </van-nav-bar>
  <view class="container">
    <view class="card base-card">
      <view class="base-card-title bold">当前孕周</view>
      <view class="base-card-content flex-between">
        <!-- 获取store中的userInfo的pregnancyWeek和pregnancyDay -->
        <view class="week-text">
          {{ userInfo.pregnancyWeeks }}周 +{{ userInfo.pregnancyDays }}天
        </view>
        <view class="cycle-text">
          {{ pregnancyType }}
        </view>
      </view>
      <!-- 今日血糖记录 -->
      <view class="today-record-card pt10">
        <view class="title bold" style="color: #ff4d76">今日血糖记录</view>
        <view class="today-record-card-content" style="display: flex; flex-wrap: wrap">
          <view
            v-for="(item, index) in todayBloodSugarRecord"
            :key="index"
            class="today-record-card-content-item"
            style="width: 50%; padding-right: 10px"
          >
            <view style="padding: 10px 0; font-size: 14px" class="bold">{{ item.name }}</view>
            <view>
              <input
                v-model="item.value"
                type="digit"
                style="width: 100%"
                :style="{ color: item.isOver ? 'red' : 'green' }"
                placeholder="请输入"
                @blur="onBlur($event, item)"
              />
            </view>
          </view>
        </view>
      </view>
      <!-- 本周数据 -->
      <view class="week-data-card card pt10 mb-20">
        <view class="title bold" style="color: #ff4d76">本周数据</view>
        <view class="p10 bold">血糖达标率</view>
        <van-progress :percentage="bloodGlucoseAchievementRate" color="#FF4D76" />
      </view>
      <view class="weight-content mt-20">
        <view class="flex-between bold" style="color: #ff4d76">
          <view class="title">体重趋势</view>
          <view style="font-size: 16px" @click="editWeightTrend">今日体重记录</view>
        </view>
        <van-dialog
          v-model:show="show"
          title="今日体重记录"
          show-cancel-button
          @confirm="confirmWeight"
        >
          <input v-model="weight" type="text" style="margin: 10px" placeholder="请输入" />
        </van-dialog>
        <view class="weight-content-chart mt-20">
          <canvas id="weightChart" style="width: 100%; height: 90%"></canvas>
          <view class="x-axis">孕期（周）</view>
        </view>
      </view>
    </view>
    <!-- 食物过敏/禁忌 -->
    <!-- <view class="food-allergy-card card pt10">
      <view class="title bold" style="color: #ff4d76">食物过敏/禁忌</view>
      <view style="display: flex; flex-wrap: wrap" class="mt-10">
        <van-tag
          v-for="(item, index) in allergyList"
          :key="index"
          style="margin-left: 5px; margin-top: 5px"
          closeable
          type="danger"
          plain
          size="large"
        >
          {{ item.foodTypeName }}
        </van-tag>
      </view>
      <button class="btn-outline" style="margin-top: 15px" @click="editFoodAllergy">添加</button>
    </view> -->
  </view>
</template>

<script setup>
import { getFoodType, saveBloodSugarInfo } from '@/api/index'
import { operateUserInfo } from '@/api/user'
import { useUserInfo } from '@/hooks/useUserInfo'
import { useUserStore } from '@/store'
import { navigateTo } from '@tarojs/taro'
import { Chart, registerables } from 'chart.js'
import { storeToRefs } from 'pinia'
import { computed, nextTick, onMounted, onUnmounted, ref, shallowRef, toRaw, watch } from 'vue'

const userStore = useUserStore()
const user = storeToRefs(userStore)
const { userInfo, mutate } = useUserInfo()
const pregnancyType = computed(() => user.pregnancyType.value || '')

Chart.register(...registerables)

const chartInstance = shallowRef(null)

const show = ref(false)
const weight = ref('')
const todayBloodSugarRecord = ref([
  {
    type: 'morningFasting',
    isOver: false, // 值是否超标
    name: '空腹血糖',
    value: '',
    origin: ''
  },
  {
    type: 'breakfastAfter',
    isOver: false,
    name: '早餐后2h',
    value: '',
    origin: ''
  },
  {
    type: 'lunchAfter',
    isOver: false,
    name: '午餐后2h',
    value: '',
    origin: ''
  },
  {
    type: 'dinnerAfter',
    isOver: false,
    name: '晚餐后2h',
    value: '',
    origin: ''
  }
])
// 定义不同时段血糖的正常阈值
const thresholdMap = {
  morningFasting: 5.3,
  breakfastAfter: 6.7,
  lunchAfter: 6.7,
  dinnerAfter: 6.7
}
const allergyList = ref([])

onMounted(() => {
  getPageData()
})

const getPageData = async () => {
  getFoodTypeData()
  // 从最新的store中获取血糖记录
  const today = new Date().toLocaleDateString('en-CA')
  const list = userInfo.value.bloodGlucoseRecord || []
  const todayData = list.find((item) => item.recordDate === today)
  if (todayData) {
    todayBloodSugarRecord.value.forEach((j) => {
      const val = todayData[j.type]
      j.value = val || ''
      j.origin = j.value
      const threshold = thresholdMap[j.type]
      if (val) {
        j.isOver = val > threshold
      }
    })
  }
}

const bloodGlucoseRecord = computed(() => userInfo.value.bloodGlucoseRecord || [])
watch(
  () => bloodGlucoseRecord.value,
  () => {
    calcBloodGlucoseAchievementRate()
  }
)

const bloodGlucoseAchievementRate = ref(0)
// 计算血糖达标率
// 3.3mmol/L<空腹/餐前血糖<5.3mmol/L
// 3.3mmol/L餐后2小时<6.7mmol/L
const calcBloodGlucoseAchievementRate = () => {
  const records = bloodGlucoseRecord.value
  // 总记录数
  let allRecordLen = 0
  // 达标记录数
  let achievementRecordLen = 0
  records.forEach((item) => {
    const { morningFasting } = item
    if (morningFasting) {
      allRecordLen += 1
      if ('3.3' < morningFasting && morningFasting < '5.3') {
        achievementRecordLen += 1
      }
    }
    const eatAfterArr = ['breakfastAfter', 'lunchAfter', 'dinnerAfter']
    eatAfterArr.forEach((af) => {
      if (item[af]) {
        allRecordLen += 1
        if ('3.3' < item[af] && item[af] < '6.7') {
          achievementRecordLen += 1
        }
      }
    })
  })
  if (allRecordLen) {
    bloodGlucoseAchievementRate.value = Math.round((achievementRecordLen / allRecordLen) * 100, 0)
  }
}

const weightRecord = computed(() => userInfo.value.weightRecord || [])
watch(
  () => weightRecord.value,
  () => {
    getWeightChangeData()
  }
)
// 获取体重变化
const getWeightChangeData = () => {
  let labels = []
  let data = []
  // 先按照pregnancyWeeks排序，再按照recordDate排序
  const records = toRaw(weightRecord.value)
  records.sort((a, b) => {
    const weekResult = a.pregnancyWeeks - b.pregnancyWeeks
    if (weekResult === 0) {
      const result = a.recordDate > b.recordDate
      return result ? -1 : 1
    }
    return weekResult
  })

  records.forEach((item) => {
    if (!labels.includes(item.pregnancyWeeks)) {
      labels.push(item.pregnancyWeeks)
      data.push(item.currentWeightKg)
    }
  })
  // 体重变化趋势折线图
  updateWeightLineChart(labels, data)
}

const updateWeightLineChart = async (labels = [], data = []) => {
  if (!chartInstance.value) {
    await createWeightLineChart()
  }

  // 更新数据
  chartInstance.value.data.labels = labels
  chartInstance.value.data.datasets[0].data = data
  chartInstance.value.update('active')
}

// 创建体重变化趋势折线图
const createWeightLineChart = () => {
  return new Promise((resolve, reject) => {
    nextTick(() => {
      const query = Taro.createSelectorQuery()
      query.exec(async () => {
        const cxt = Taro.createCanvasContext('weightChart', this)
        chartInstance.value = new Chart(cxt, {
          type: 'line',
          data: {
            labels: [],
            datasets: [
              {
                label: '体重变化趋势',
                data: [],
                backgroundColor: 'rgba(255, 77, 118, 0.2)',
                borderColor: '#FF4D76',
                fill: true
              }
            ]
          },
          options: {
            responsive: true
          }
        })
        resolve()
      })
    })
  })
}
const confirmWeight = () => {
  const newInfo = {
    ...userInfo.value,
    currentWeightKg: weight.value
  }

  operateUserInfo(newInfo).then((res) => {
    Taro.showToast({
      title: '保存成功',
      icon: 'success'
    })
    mutate()
  })
}
const editWeightTrend = () => {
  show.value = true
}

const editFoodAllergy = () => {
  navigateTo({ url: '/pages/home/<USER>' })
}

// 获取食材类型
const getFoodTypeData = () => {
  const allergies = []
  getFoodType().then((res) => {
    // 获取过敏食物
    res.Content.forEach((item) => {
      item.foodTypeResults.forEach((j) => {
        if (userInfo.value.foodAllergy && userInfo.value.foodAllergy.includes(j.foodTypeID)) {
          allergies.push(j)
        }
      })
    })
    allergyList.value = allergies
  })
}

const onBlur = (e, item) => {
  const threshold = thresholdMap[item.type]
  const val = e.target.value
  // 输入内容和原始值相等，内容不变，不需要保存
  const origin = todayBloodSugarRecord.value.find((r) => r.type === item.type)
  if (origin.origin == val) {
    return
  }

  todayBloodSugarRecord.value.forEach((i) => {
    if (i.name === item.name) {
      i.isOver = val > threshold ? true : false
    }
  })
  const data = {
    womanId: userInfo.value.id,
    recordDate: new Date().toLocaleDateString().replace(/\//g, '-')
  }
  data[item.type] = item.value
  saveBloodSugarInfo(data).then((res) => {
    if (threshold && val > threshold) {
      Taro.showToast({
        title: '血糖值超出正常值，请注意！',
        icon: 'none',
        duration: 2000
      })
    } else {
      Taro.showToast({
        title: '保存成功',
        icon: 'none'
      })
    }
    item.origin = val
    mutate()
  })
}
const editUserInfo = () => {
  navigateTo({ url: 'pages/home/<USER>' })
}

onUnmounted(() => {
  if (chartInstance.value) {
    // 在销毁前先停止所有动画
    chartInstance.value.stop()
    chartInstance.value.destroy()
    chartInstance.value = null
  }
})
</script>
<style lang="scss" scoped>
.card {
  padding: 16px;
}

.base-card {
  padding: 20px;
  background-color: #fef3f6;
  border-radius: 6px;
  box-shadow: 0 0 5px 0 rgba(141, 135, 135, 0.1);

  .base-card-title {
    font-size: 32px;
    color: #868282;
  }

  .base-card-content {
    padding: 15px 0;

    .week-text {
      font-size: 46px;
    }

    .cycle-text {
      font-size: 28px;
      color: #ff85a2;
    }
  }

  .weight-content-chart {
    width: 100%;
    min-height: 360px;
    border-radius: 6px;
    background-color: #fff;
  }

  .x-axis {
    font-size: 28px;
    color: #ff85a2;
    text-align: center;
  }
}

.btn-outline {
  padding: 0;
  font-size: 32px;
}

.title {
  font-size: 32px;
}
</style>
