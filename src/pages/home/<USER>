<!-- 用户膳食画像修改 -->
<template>
  <view>
    <van-tabs sticky>
      <van-tab title="个人偏好">
        <view id="preference-page" class="container">
          <view class="form-group">
            <label class="option-label required">餐次选择</label>
            <view class="option-grid">
              <view
                v-for="item in mealTimes"
                :key="item.value"
                class="option-btn"
                :class="{ active: editInfo.mealPlan === item.value }"
                @click="handleSelected(item)"
              >
                {{ item.label }}
              </view>
            </view>
          </view>

          <view class="form-group">
            <label class="option-label required">口味偏好</label>
            <view class="option-grid">
              <view
                v-for="item in taste"
                :key="item.value"
                class="option-btn"
                :class="{ active: editInfo.tastePreference === item.value }"
                @click="handleSelected(item)"
              >
                {{ item.label }}
              </view>
            </view>
          </view>

          <view class="form-group">
            <label class="option-label required">主食偏好</label>
            <view class="option-grid col-2">
              <view
                v-for="item in staple"
                :key="item.value"
                class="option-btn"
                :class="{ active: editInfo.stapleFoodPreference === item.value }"
                @click="handleSelected(item)"
              >
                {{ item.label }}
              </view>
            </view>
          </view>

          <view class="form-group">
            <label class="option-label required">奶类偏好</label>
            <view class="option-grid col-3">
              <view
                v-for="item in dairy"
                :key="item.value"
                class="option-btn"
                :class="{ active: editInfo.dairyPreference === item.value }"
                @click="handleSelected(item)"
              >
                {{ item.label }}
              </view>
            </view>
          </view>

          <view class="form-group">
            <label class="option-label required">肉类偏好</label>
            <view class="option-grid col-3">
              <view
                v-for="item in meat"
                :key="item.value"
                class="option-btn"
                :class="{ active: editInfo.meatPreference === item.value }"
                @click="handleSelected(item)"
              >
                {{ item.label }}
              </view>
            </view>
          </view>
          <view class="action-buttons">
            <button class="btn-outline" @click="onBack">取消</button>
            <button class="btn-primary" @click="onSave">保存</button>
          </view>
        </view>
      </van-tab>
      <van-tab title="食物禁忌">
        <view id="food-taboo-page" class="container">
          <view v-for="item in foodTypeList" :key="item.foodTypeID" class="form-group">
            <label class="option-label">{{ item.foodTypeName }}</label>
            <view class="option-grid">
              <view
                v-for="j in item.foodTypeResults"
                :key="j.foodTypeID"
                class="option-btn"
                :class="{ active: editInfo.foodAllergy?.includes(j.foodTypeID) }"
                @click="handleSelectedAllergy(j)"
              >
                {{ j.foodTypeName }}
              </view>
            </view>
          </view>
          <view class="form-group">
            <label class="option-label">其它食材</label>
            <view class="">
              <input
                v-model="editInfo.otherAllergy"
                type="text"
                style="width: 100%"
                placeholder="请输入其他过敏食物或禁忌"
                @input="getFoodListData(editInfo.otherAllergy)"
              />
            </view>
          </view>
          <view class="action-buttons">
            <button class="btn-outline" @click="onBack">取消</button>
            <button class="btn-primary" @click="onSave">保存</button>
          </view>
        </view>
      </van-tab>
    </van-tabs>
  </view>
</template>
<script setup>
import { getFoodList, getFoodType } from '@/api/index'
import { operateUserInfo } from '@/api/user'
import { useUserStore } from '@/store'
import { navigateTo } from '@tarojs/taro'
import { onMounted, reactive, ref } from 'vue'

const mealTimes = [
  {
    label: '3次正餐+3次加餐',
    value: '0',
    type: 'mealPlan'
  },
  {
    label: '3次正餐+2次加餐',
    value: '1',
    type: 'mealPlan'
  }
]
const taste = [
  {
    label: '可以接受辣椒',
    value: '0',
    type: 'tastePreference'
  },
  {
    label: '不能接受辣椒',
    value: '1',
    type: 'tastePreference'
  }
]
const staple = [
  {
    label: '偏好米面类',
    value: '0',
    type: 'stapleFoodPreference'
  },
  {
    label: '偏好薯类',
    value: '1',
    type: 'stapleFoodPreference'
  }
]
const dairy = [
  {
    label: '牛奶',
    value: '0',
    type: 'dairyPreference'
  },
  {
    label: '酸奶',
    value: '1',
    type: 'dairyPreference'
  },
  {
    label: '均可',
    value: '2',
    type: 'dairyPreference'
  }
]
const meat = [
  {
    label: '畜禽类',
    value: '0',
    type: 'meatPreference'
  },
  {
    label: '海产品类',
    value: '1',
    type: 'meatPreference'
  },
  {
    label: '均可',
    value: '2',
    type: 'meatPreference'
  }
]

// 编辑信息
const editInfo = reactive({
  id: useUserStore().getUserInfo().id,
  mealPlan: '0',
  tastePreference: '0',
  stapleFoodPreference: '0',
  dairyPreference: '0',
  meatPreference: '0',
  foodAllergy: [],
  otherAllergy: ''
})

const foodTypeList = ref([])

onMounted(() => {
  // 获取store用户信息
  Object.assign(editInfo, useUserStore().getUserInfo())
  getFoodTypeData()
})
const handleSelected = (item) => {
  console.log('item', item)
  editInfo[item.type] = item.value
}

const handleSelectedAllergy = (item) => {
  if (editInfo.foodAllergy && editInfo.foodAllergy.includes(item.foodTypeID)) {
    editInfo.foodAllergy = editInfo.foodAllergy.filter((id) => id !== item.foodTypeID)
  } else {
    editInfo.foodAllergy = editInfo.foodAllergy || []
    editInfo.foodAllergy.push(item.foodTypeID)
  }
}
const getFoodTypeData = () => {
  getFoodType().then((res) => {
    foodTypeList.value = res.Content.filter((item) => item.foodTypeName !== '烹调油和盐')
  })
}

const getFoodListData = (value) => {
  console.log('value', value)

  const params = {
    foodName: value,
    pageNum: 1,
    pageSize: 10
  }
  getFoodList(params).then((res) => {
    console.log('res', res)
  })
}

const onBack = () => {
  navigateTo({ url: 'pages/home/<USER>' })
}
const onSave = () => {
  operateUserInfo(editInfo).then((res) => {
    Taro.showToast({
      title: '保存成功',
      icon: 'success',
      duration: 2000
    })
    setTimeout(() => {
      navigateTo({ url: '/pages/home/<USER>' })
    }, 1000)
  })
}
</script>

<style lang="scss" scoped>
.container {
  padding-top: 20px;
}

.option-grid {
  padding-top: 10px;

  .active {
    background-color: #ed6c97;
    color: #fff;
  }
}
</style>
