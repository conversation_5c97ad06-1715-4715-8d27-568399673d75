<template>
  <!-- 登录页面 -->
  <view class="login-page">
    <view class="avatar text-center">
      <i class="ri-user-pregnant-line"><van-icon name="manager-o" size="60" /></i>
    </view>
    <view class="logo-container">
      <view class="logo-text">妊娠期糖尿病膳食推荐平台</view>
    </view>
    <form @submit="formSubmit">
      <view class="taro-example-body" style="margin-top: 60px; padding: 0 6px">
        <input
          v-model="submitForm.phone"
          type="text"
          placeholder="请输入手机号"
          :focus="true"
          style="font-size: 16px; padding: 12px 16px"
        />
        <view
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 18px;
          "
        >
          <input
            v-model="submitForm.verifyCode"
            placeholder="验证码:546434"
            style="width: 100%; font-size: 16px; padding: 12px 16px; background-color: #f0ebeb"
            disabled
          />
          <!-- <Button
            :style="codeMsg.disabled ? 'width: 30%;margin-left: 10px;border-radius:5px;border: 1px solid #CCC;font-size: 14px;height:36px;' : 'background-color: #e5457a;color: #fff;width: 30%;margin-left: 10px;border: 1px solid #e5457a;border-radius: 8px;font-size: 14px;height:36px;'"
            @click="getCode" :disabled="codeMsg.disabled">{{ buttonText }}</Button> -->
        </view>
        <view style="font-size: 14px; margin-top: 10px">请使用固定验证码登录：546434</view>
      </view>
      <view class="taro-example-btns">
        <button form-type="submit" style="background-color: #e5457a; color: #fff">登录</button>
      </view>
    </form>

    <view class="mt-20" style="display: flex; justify-content: flex-end">
      <!-- <a href="#" class="link" @click="handleRegister">注册账号</a> -->
      <!-- <a href="#" class="link">忘记密码?</a> -->
    </view>
  </view>
</template>
<script setup>
import { login } from '@/api/index'
import { useUserStore } from '@/store'
import { navigateTo, redirectTo } from '@tarojs/taro'
import { computed, ref } from 'vue'

const submitForm = ref({
  phone: '',
  verifyCode: '546434'
})
const codeMsg = ref({
  text: '验证码',
  disabled: false,
  seconds: 60
})

// 登录
const formSubmit = () => {
  if (!submitForm.value.phone) {
    Taro.showToast({
      title: '请输入手机号',
      icon: 'error'
    })
    return
  }
  if (!validatePhoneNumber(submitForm.value.phone)) {
    Taro.showToast({
      title: '手机格式有误',
      icon: 'error'
    })
    return
  }

  login(submitForm.value).then((res) => {
    Taro.showToast({
      title: '登录成功',
      icon: 'success'
    })
    if (res.Content) {
      // 若是老用户，则跳转至首页
      useUserStore().setUserInfo({
        ...res.Content
      })
      redirectTo({
        url: '/pages/home/<USER>'
      })
    } else {
      // 若是新用户，则跳转至孕妇膳食画像收集页面
      useUserStore().setUserInfo({
        phone: submitForm.value.phone
      })
      navigateTo({
        url: '/pages/userInfo/step1'
      })
    }
  })
}

// 判断手机格式是否正确
const validatePhoneNumber = (phoneNum) => {
  const reg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/
  return reg.test(phoneNum)
}

const buttonText = computed(() => {
  return codeMsg.value.disabled ? `${codeMsg.value.seconds}s` : codeMsg.value.text
})

// 获取验证码
const getCode = () => {
  codeMsg.value.disabled = true
  codeMsg.value.seconds = 60
  codeMsg.value.text = '获取验证码'
  const timer = setInterval(() => {
    codeMsg.value.seconds--
    codeMsg.value.text = `${codeMsg.value.seconds}s`
    if (codeMsg.value.seconds < 0) {
      clearInterval(timer)
      codeMsg.value.disabled = false
      codeMsg.value.text = '验证码'
    }
  }, 1000)
}
</script>

<style lang="scss" scoped>
.login-page {
  height: 100%;
  padding: 50px;
  background-color: #f5f5f5;

  .avatar {
    margin-top: 100px;
    color: #e5457a;
  }

  .logo-container {
    text-align: center;
    margin: 40px 0;

    .logo-text {
      color: #e5457a;
      font-size: 32px;
      margin-top: 10px;
    }
  }
}

.link {
  color: #e5457a;
  font-size: 28px;
}

.taro-example-btns {
  margin-top: 60px;
}

.input-group .btn-toggle-pwd {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  width: 24px;
  height: 24px;
  opacity: 0.5;
}
</style>
