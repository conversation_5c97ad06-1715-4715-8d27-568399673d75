<template>
  <!-- 完善个人信息页面 -->
  <van-nav-bar title="完善个人信息" fixed />
  <div class="container active">
    <div class="step-indicator">
      <div class="step active">1</div>
      <div class="step-line"></div>
      <div class="step">2</div>
    </div>

    <div class="form-group" style="margin-top: 20px">
      <label class="required">姓名</label>
      <input v-model="userInfo.name" type="text" placeholder="请输入您的姓名" />
    </div>

    <div class="form-group">
      <label class="required">身高</label>
      <div class="input-group">
        <input v-model="userInfo.heightCm" type="number" placeholder="请输入身高" />
        <span class="unit">cm</span>
      </div>
    </div>

    <div class="form-group">
      <label class="required">孕前体重</label>
      <div class="input-group">
        <input v-model="userInfo.prePregnancyWeightKg" type="number" placeholder="请输入孕前体重" />
        <span class="unit">kg</span>
      </div>
    </div>

    <div class="form-group">
      <label class="required">目前体重</label>
      <div class="input-group">
        <input v-model="userInfo.currentWeightKg" type="number" placeholder="请输入目前体重" />
        <span class="unit">kg</span>
      </div>
    </div>

    <div class="form-group">
      <label class="required">孕周</label>
      <div class="option-grid" style="display: flex; align-items: center">
        <input v-model="userInfo.pregnancyWeeks" type="number" placeholder="孕周" />
        <span>-</span>
        <input v-model="userInfo.pregnancyDays" type="number" placeholder="孕天" />
      </div>
    </div>

    <button class="btn-primary" @click="handleNext">下一步</button>
  </div>
</template>

<script setup>
import { useUserStore } from '@/store'
import { navigateTo } from '@tarojs/taro'
import { storeToRefs } from 'pinia'

const userStore = useUserStore()
const user = storeToRefs(userStore)
const userInfo = user.userInfo
console.log('🚀 >>>> userInfo>>>>', userInfo)

const handleNext = () => {
  // 表单验证
  if (
    !userInfo.value.name ||
    !userInfo.value.heightCm ||
    !userInfo.value.prePregnancyWeightKg ||
    !userInfo.value.currentWeightKg ||
    !userInfo.value.pregnancyWeeks ||
    !userInfo.value.pregnancyDays
  ) {
    Taro.showToast({
      title: '请填写完整信息',
      icon: 'none'
    })
    return
  }
  navigateTo({ url: '/pages/userInfo/step2' })
}
</script>
<style scoped lang="scss">
.input-style {
  width: 100%;
  background: #fff;
  border-radius: 16px;
  font-size: 28px;
  padding: 15px;
  border: 1px solid #e0e0e0;
}
</style>
