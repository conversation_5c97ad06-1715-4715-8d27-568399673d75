<template>
  <!-- 食物禁忌页面 -->
  <van-nav-bar title="食物禁忌" fixed />
  <div id="food-taboo-page" class="container active">
    <div class="step-indicator">
      <div class="step active">1</div>
      <div class="step-line active"></div>
      <div class="step active">2</div>
      <div class="step-line active"></div>
      <div class="step active">3</div>
    </div>

    <div v-for="item in foodTypeList" :key="item.foodTypeID" class="form-group">
      <label class="option-label">{{ item.foodTypeName }}</label>
      <div class="option-grid">
        <div
          v-for="j in item.foodTypeResults"
          :key="j.foodTypeID"
          class="option-btn"
          :class="{ active: FormData.foodAllergy.includes(j.foodTypeID) }"
          @click="handleSelectedAllergy(j)"
        >
          {{ j.foodTypeName }}
        </div>
      </div>
    </div>

    <div class="form-group">
      <label class="option-label">其它食材</label>
      <div class="">
        <input
          v-model="FormData.otherAllergy"
          type="text"
          style="width: 100%"
          placeholder="请输入其他过敏食物或禁忌"
        />
      </div>
    </div>
    <div class="action-buttons">
      <button class="btn-outline" @click="handleBack">上一步</button>
      <button class="btn-primary" @click="handleNext">完成</button>
    </div>
  </div>
</template>

<script setup>
import { getFoodType } from '@/api/index'
import { operateUserInfo } from '@/api/user'
import { useUserStore } from '@/store'
import { navigateTo } from '@tarojs/taro'
import { onMounted, reactive, ref } from 'vue'

const FormData = reactive({
  foodAllergy: [],
  otherAllergy: ''
})

const foodTypeList = ref([])

onMounted(() => {
  getFoodTypeData()
})

const getFoodTypeData = () => {
  getFoodType().then((res) => {
    foodTypeList.value = res.Content.filter((item) => item.foodTypeName !== '烹调油和盐')
  })
}

const handleSelectedAllergy = (item) => {
  if (FormData.foodAllergy.includes(item.foodTypeID)) {
    FormData.foodAllergy = FormData.foodAllergy.filter((id) => id !== item.foodTypeID)
  } else {
    FormData.foodAllergy.push(item.foodTypeID)
  }
}
const handleBack = () => {
  navigateTo({ url: '/pages/userInfo/step2' })
}

const handleNext = () => {
  console.log(useUserStore().getUserInfo())
  // 将FormData中的数据更新到store中
  useUserStore().setUserInfo({
    ...useUserStore().getUserInfo(),
    ...FormData.value
  })
  // 新增孕妇膳食画像信息
  operateUserInfo({
    ...useUserStore().getUserInfo()
  }).then((res) => {
    Taro.showToast({
      title: '保存成功',
      icon: 'success',
      duration: 2000
    })
    console.log('res>>', res)
    // 设置id
    useUserStore().setUserInfo({
      ...useUserStore().getUserInfo(),
      id: res.Content
    })
    setTimeout(() => {
      navigateTo({ url: '/pages/home/<USER>' })
    }, 1000)
  })
}
</script>
<style lang="scss" scoped>
.option-grid {
  padding-top: 10px;

  .active {
    background-color: #ed6c97;
    color: #fff;
  }
}
</style>
