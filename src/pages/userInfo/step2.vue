<template>
  <!-- 个人偏好页面 -->
  <van-nav-bar title="个人偏好" fixed />
  <div id="preference-page" class="container active">
    <div class="step-indicator">
      <div class="step active">1</div>
      <div class="step-line active"></div>
      <div class="step active">2</div>
    </div>

    <div class="form-group" style="margin-top: 20px">
      <label class="option-label required">餐次选择</label>
      <div class="option-grid">
        <div
          v-for="item in mealTimes"
          :key="item.value"
          class="option-btn"
          :class="{ active: userInfo.mealPlan === item.value }"
          @click="handleSelected(item)"
        >
          {{ item.label }}
        </div>
      </div>
    </div>

    <div class="form-group">
      <label class="option-label required">口味偏好</label>
      <div class="option-grid">
        <div
          v-for="item in taste"
          :key="item.value"
          class="option-btn"
          :class="{ active: userInfo.tastePreference === item.value }"
          @click="handleSelected(item)"
        >
          {{ item.label }}
        </div>
      </div>
    </div>

    <div class="form-group">
      <label class="option-label required">主食偏好</label>
      <div class="option-grid col-2">
        <div
          v-for="item in staple"
          :key="item.value"
          class="option-btn"
          :class="{ active: userInfo.stapleFoodPreference === item.value }"
          @click="handleSelected(item)"
        >
          {{ item.label }}
        </div>
      </div>
    </div>

    <div class="form-group">
      <label class="option-label required">奶类偏好</label>
      <div class="option-grid col-3">
        <div
          v-for="item in dairy"
          :key="item.value"
          class="option-btn"
          :class="{ active: userInfo.dairyPreference === item.value }"
          @click="handleSelected(item)"
        >
          {{ item.label }}
        </div>
      </div>
    </div>

    <div class="form-group">
      <label class="option-label required">肉类偏好</label>
      <div class="option-grid col-3">
        <div
          v-for="item in meat"
          :key="item.value"
          class="option-btn"
          :class="{ active: userInfo.meatPreference === item.value }"
          @click="handleSelected(item)"
        >
          {{ item.label }}
        </div>
      </div>
    </div>

    <div class="form-group">
      <label class="option-label">饮食禁忌</label>
      <div class="">
        <input
          v-model="userInfo.otherAllergy"
          type="textarea"
          style="width: 100%"
          placeholder="请输入其他过敏食物或禁忌"
        />
      </div>
    </div>

    <div class="action-buttons">
      <button class="btn-outline" @click="handleBack">上一步</button>
      <button class="btn-primary" @click="handleNext">完成</button>
    </div>
  </div>
</template>

<script setup>
import { operateUserInfo } from '@/api/user'
import { useUserStore } from '@/store'
import { navigateBack, switchTab } from '@tarojs/taro'
import { storeToRefs } from 'pinia'
import { ref } from 'vue'

defineOptions({
  inheritAttrs: false
})

const mealTimes = ref([
  {
    label: '3次正餐+3次加餐',
    value: '0',
    type: 'mealPlan'
  },
  {
    label: '3次正餐+2次加餐',
    value: '1',
    type: 'mealPlan'
  }
])

const taste = ref([
  {
    label: '可以接受辣椒',
    value: '0',
    type: 'tastePreference'
  },
  {
    label: '不能接受辣椒',
    value: '1',
    type: 'tastePreference'
  }
])

const staple = ref([
  {
    label: '偏好米面类',
    value: '0',
    type: 'stapleFoodPreference'
  },
  {
    label: '偏好薯类',
    value: '1',
    type: 'stapleFoodPreference'
  }
])

const dairy = ref([
  {
    label: '牛奶',
    value: '0',
    type: 'dairyPreference'
  },
  {
    label: '酸奶',
    value: '1',
    type: 'dairyPreference'
  },
  {
    label: '均可',
    value: '2',
    type: 'dairyPreference'
  }
])

const meat = ref([
  {
    label: '畜禽类',
    value: '0',
    type: 'meatPreference'
  },
  {
    label: '海产品类',
    value: '1',
    type: 'meatPreference'
  },
  {
    label: '均可',
    value: '2',
    type: 'meatPreference'
  }
])

const userStore = useUserStore()
const user = storeToRefs(userStore)
const userInfo = user.userInfo

const handleSelected = (item) => {
  userInfo.value[item.type] = item.value
}

const handleBack = () => {
  navigateBack()
}

const handleNext = () => {
  // 新增孕妇膳食画像信息
  operateUserInfo({
    ...userInfo.value
  }).then((res) => {
    Taro.showToast({
      title: '保存成功',
      icon: 'success',
      duration: 2000
    })
    console.log('res>>', res)
    // 设置id
    userStore.setUserInfo({
      id: res.Content
    })
    setTimeout(() => {
      switchTab({ url: '/pages/home/<USER>' })
    }, 1000)
  })
}
</script>
<style lang="scss" scoped>
.container {
  // padding: 20px;
}

.option-grid {
  .active {
    background-color: #ed6c97;
    color: #fff;
  }
}
</style>
