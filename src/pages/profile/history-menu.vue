<!-- 历史菜谱 -->
<template>
  <van-nav-bar title="历史菜谱" fixed left-arrow @click-left="onClickLeft" />
  <view class="container">
    <view class="flex-between select-date">
      <van-cell title="选择历史菜谱日期" :value="selectDate" @click="show = true" />
      <van-icon name="arrow" />
    </view>
    <van-calendar
      v-model:show="show"
      :max-date="maxDate"
      :min-date="new Date(1990, 0, 1)"
      color="#FF85A2"
      @confirm="onConfirm"
    />
    <van-empty
      v-if="!menuList.childrenList || menuList.childrenList.length === 0"
      description="暂无菜谱数据"
    />
    <view v-else class="menu-list mt-10">
      <view v-for="item in menuList.childrenList" :key="item.id" class="menu-list-item">
        <view class="menu-list-item-top flex justify-between">
          <view class="meal-plan-value">{{ item.mealPlanValue }}</view>
          <view style="font-size: 16px">{{ item.date }}</view>
        </view>
        <view class="menu-list-item-bottom">
          <view class="flex justify-between">
            <view class="menu-name">{{ item.menuName }}</view>
            <view @click="handleCollect(item)">
              <van-icon v-if="!item.isCollect" name="like-o" />
              <van-icon v-else name="like" color="#ef4444" />
            </view>
          </view>
          <view class="ingredients">{{ item.ingredients }}</view>
        </view>
      </view>
    </view>
  </view>
</template>
<script setup>
import { getMenuList } from '@/api/index'
import { useUserStore } from '@/store'
import { navigateTo } from '@tarojs/taro'
import { onMounted, ref } from 'vue'

const show = ref(false)
const maxDate = ref(new Date())
// 默认获取当日日期格式为2025-05-14
const selectDate = ref(new Date().toLocaleDateString().replace(/\//g, '-'))

const menuList = ref({
  childrenList: []
})

onMounted(() => {
  getMenuListData()
})

const getMenuListData = async () => {
  const data = {
    id: useUserStore().userInfo.id,
    recordDate: selectDate.value,
    pageNum: 1,
    pageSize: 10
  }
  getMenuList(data).then((res) => {
    menuList.value = res.Content[0] || {}
  })
}

// 收藏菜谱
const handleCollect = (item) => {
  const data = {
    id: useUserStore().userInfo.id,
    menuId: item.id,
    isCollect: item.isCollect ? 0 : 1
  }
  menuList.value.forEach((j) => {
    if (j.menuName === item.menuName) {
      j.isCollect = !j.isCollect
    }
  })
}
const formatDate = (date) => {
  return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`
}
const onConfirm = (date) => {
  show.value = false
  selectDate.value = formatDate(date)
  getMenuListData()
}
const onClickLeft = () => {
  navigateTo({ url: 'pages/profile/index' })
}
</script>

<style lang="scss" scoped>
.select-date {
  background-color: #fff;
}

.menu-list {
  padding: 10px;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  border-radius: 10px;

  .menu-list-item {
    padding: 20px 10px;
    border-bottom: 1px solid #f0f0f0;

    .meal-plan-value {
      color: #ff4d76;
      font-size: 32px;
      font-weight: bold;
    }

    .menu-name {
      font-size: 28px;
      font-weight: bold;
      padding: 10px 0;
    }

    .ingredients {
      font-size: 28px;
    }
  }
}
</style>
