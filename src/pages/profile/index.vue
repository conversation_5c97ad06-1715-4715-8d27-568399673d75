<!-- 个人中心 -->
<template>
  <!-- <van-nav-bar title="个人中心" fixed /> -->
  <view class="container" style="padding-top: 20px">
    <view class="user-profile">
      <view class="avatar">
        <i class="ri-user-pregnant-line"><van-icon name="contact-o" size="40" /></i>
      </view>
      <view class="username">{{ userInfo.name }}</view>
    </view>

    <view class="card user-info">
      <view class="flex-between mb-20">
        <view class="card-title">个人信息</view>
        <view class="edit-profile-btn flex-center" @click="openEditProfile()">查看</view>
      </view>
      <view class="info-row">
        <span class="info-label">当前孕周</span>
        <span class="info-value">
          {{ userInfo.pregnancyWeeks }}周+{{
            userInfo.pregnancyDays ? userInfo.pregnancyDays : 0
          }}天 ({{ pregnancyType }})
        </span>
      </view>

      <view class="info-row">
        <span class="info-label">身高</span>
        <span class="info-value">{{ userInfo.heightCm }}cm</span>
      </view>

      <view class="info-row">
        <span class="info-label">体重</span>
        <span class="info-value">{{ userInfo.currentWeightKg }}kg</span>
      </view>
    </view>
    <view class="card">
      <view class="settings-list">
        <view class="settings-item" @click="goToNotice">
          <van-icon name="chat" />
          <span>通知中心</span>
          <van-icon name="arrow" size="15" />
        </view>
        <view class="settings-item" @click="goToHistoryMenu">
          <van-icon name="column" />
          <span>历史菜谱</span>
          <van-icon name="arrow" size="15" />
        </view>
      </view>
    </view>
    <view class="logout">
      <button class="btn-primary" @click="logout">退出登录</button>
    </view>
  </view>
</template>

<script setup>
import { useUserStore } from '@/store'
import { navigateTo, redirectTo } from '@tarojs/taro'
import { storeToRefs } from 'pinia'
import { showConfirmDialog } from 'vant'
import { computed, onMounted } from 'vue'

const userStore = useUserStore()
const user = storeToRefs(userStore)
const userInfo = computed(() => {
  return user.userInfo.value || {}
})
const pregnancyType = computed(() => {
  return user.pregnancyType.value || ''
})

// 编辑个人信息
const openEditProfile = () => {
  navigateTo({ url: 'pages/profile/edit-profile' })
}

// 通知中心
const goToNotice = () => {
  navigateTo({ url: 'pages/profile/notice' })
}

// 历史菜谱
const goToHistoryMenu = () => {
  navigateTo({ url: 'pages/profile/history-menu' })
}

// 退出登录
const logout = () => {
  showConfirmDialog({
    title: '提示',
    message: '是否要退出登录'
  })
    .then(() => {
      // on confirm
      navigateTo({ url: 'pages/login/index' })
    })
    .catch(() => {
      // on cancel
    })
}
onMounted(() => {
  if (!userInfo.value.id) {
    redirectTo({ url: '/pages/login/index' })
  }
})
</script>

<style lang="scss" scoped>
@use '../../styles/variables.scss' as *;

.card {
  background: $background;
  border-radius: $radius-lg;
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.05),
    0 1px 2px rgba(0, 0, 0, 0.1);
  padding: 24px;
  margin-bottom: 10px;
  border: 1px solid $border;
  transition: $transition;
}

.card:hover {
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.05),
    0 2px 4px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.card-title {
  color: $text;
  font-size: 38px;
  font-weight: bold;
}

.user-profile {
  text-align: center;
  padding: 20px 0;
}

.avatar {
  width: 160px;
  height: 160px;
  border-radius: 50%;
  background: $primary-light;
  margin: 0 auto 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30px;
  color: white;
}

.username {
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 5px;
}

.user-info {
  position: relative;
}

.edit-btn {
  position: absolute;
  right: 0;
  top: 0;
  color: $primary;
  font-size: 14px;
  background: none;
  border: none;
  cursor: pointer;
}

.info-row {
  display: flex;
  justify-content: space-between;
  padding: 24px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  color: $gray;
  font-size: 28px;
}

.info-value {
  font-size: 28px;
  font-weight: 500;
}

.settings-item {
  display: flex;
  align-items: center;
  padding: 30px 0;
  border-bottom: 1px solid #f0f0f0;
}

.settings-item i {
  margin-right: 15px;
  color: $gray;
}

.settings-item span {
  flex: 1;
  font-size: 28px;
}

.settings-item .arrow {
  color: $gray;
}

.edit-profile-btn {
  width: 120px;
  height: 60px;
  font-size: 24px;
  border-radius: $radius-full;
  background: $primary-light;
  color: $primary-dark;
}

.edit-profile-btn:hover {
  background: $primary;
  color: white;
}

.logout {
}
</style>
