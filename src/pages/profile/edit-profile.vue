<template>
  <van-nav-bar title="个人信息" fixed />
  <div class="edit-profile container">
    <div class="form-group">
      <label>姓名</label>
      <input v-model="userInfo.name" type="text" disabled />
    </div>

    <div class="form-group">
      <label>身高</label>
      <div class="input-group">
        <input v-model="userInfo.heightCm" type="number" disabled />
        <span class="unit">cm</span>
      </div>
    </div>

    <div class="form-group">
      <label>孕前体重</label>
      <div class="input-group">
        <input v-model="userInfo.prePregnancyWeightKg" type="number" disabled />
        <span class="unit">kg</span>
      </div>
    </div>

    <div class="form-group">
      <label>目前体重</label>
      <div class="input-group">
        <input v-model="userInfo.currentWeightKg" type="number" disabled />
        <span class="unit">kg</span>
      </div>
    </div>

    <div class="form-group">
      <label>孕周</label>
      <div class="option-grid" style="display: flex; align-items: center">
        <input v-model="userInfo.pregnancyWeeks" type="number" disabled />
        <span>-</span>
        <input v-model="userInfo.pregnancyDays" type="number" disabled />
      </div>
    </div>
    <div class="action-buttons">
      <button class="btn-primary" @click="handleBack">返回</button>
      <!-- <button class="btn-primary" @click="handleNext">保存</button> -->
    </div>
  </div>
</template>

<script setup>
import { operateUserInfo } from '@/api/user'
import { useUserStore } from '@/store'
import { navigateTo } from '@tarojs/taro'
import { onMounted, ref } from 'vue'

const userInfo = ref({
  id: '',
  name: '',
  heightCm: '',
  prePregnancyWeightKg: '',
  currentWeightKg: '',
  pregnancyWeeks: '',
  pregnancyDays: ''
})

onMounted(() => {
  // 获取用户信息
  Object.assign(userInfo.value, useUserStore().getUserInfo())
})

// 返回
const handleBack = () => {
  navigateTo({
    url: '/pages/profile/index'
  })
}

// 保存
const handleNext = () => {
  // 验证必填项
  if (
    !userInfo.value.name ||
    !userInfo.value.heightCm ||
    !userInfo.value.prePregnancyWeightKg ||
    !userInfo.value.currentWeightKg ||
    !userInfo.value.pregnancyWeeks ||
    !userInfo.value.pregnancyDays
  ) {
    Taro.showToast({
      title: '请填写必填项',
      icon: 'none'
    })
    return
  }
  operateUserInfo(userInfo.value).then((res) => {
    Taro.showToast({
      title: '保存成功',
      icon: 'success'
    })
    // 更新store中对应参数
    useUserStore().setUserInfo(userInfo.value)
    setTimeout(() => {
      navigateTo({
        url: '/pages/profile/index'
      })
    }, 1000)
  })
}
</script>
