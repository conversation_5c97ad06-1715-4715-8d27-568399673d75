<!-- 通知中心 -->
<template>
  <van-nav-bar title="通知中心" fixed left-arrow @click-left="onClickLeft" />
  <div class="container">
    <div v-for="item in noticeList" :key="item.id" class="notice-item">
      <div class="notice-item-top flex-between">
        <div class="notice-item-top-left">
          <van-icon color="#e5446d" name="bell" size="20" />
          <span style="margin-left: 6px; font-size: 16px">{{ item.title }}</span>
        </div>
        <div class="notice-item-top-right">
          <div style="font-size: 16px">{{ item.time }}</div>
        </div>
      </div>
      <div class="notice-item-bottom">
        <span>{{ item.content }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { navigateTo, Taro } from '@tarojs/taro'
const noticeList = [
  {
    id: 1,
    title: '系统通知',
    content: '该记录您的血糖值了，保持规律记录有助于更好的管理健康',
    time: '12:00'
  },
  {
    id: 2,
    title: '系统通知',
    content: '您有新的消息',
    time: '12:00'
  }
]
const onClickLeft = () => {
  navigateTo({ url: 'pages/profile/index' })
}
</script>

<style lang="scss" scoped>
.container {
  .notice-item {
    background: #fff;
    padding: 10px;
    border-radius: 10px;
    margin-bottom: 10px;

    .notice-item-top {
      font-size: 16px;
      color: #676565;
      padding: 10px 0;
    }

    .notice-item-bottom {
      font-size: 32px;
      font-weight: bold;
    }
  }
}
</style>
