<template>
  <van-nav-bar title="今日菜谱" fixed />
  <view class="container">
    <view class="menu-header flex-between">
      <view class="menu-header-left">
        <view class="bold">{{ menuInfo.recommendQuantity }}Kcal</view>
        <view style="padding-top: 10px; color: #9a9fa9; font-size: 16px">每日建议摄入能量</view>
      </view>
      <view class="menu-header-right" style="color: #9a9fa9; font-size: 16px">
        今日食材
        <span style="color: green">{{ menuInfo.foodQuantity }}</span>
        种
      </view>
    </view>

    <!-- 饼图区域 -->
    <view class="charts-container">
      <view class="charts-row">
        <view class="chart-item">
          <PieChart
            title="营养素占比饼图"
            :data="nutritionData"
            :colors="['#f7be7d', '#60b8eb', '#6ac4af']"
            chart-id="nutrition-chart"
            :value-data="nutritionValueData"
          />
        </view>
        <view class="chart-item">
          <PieChart
            title="三餐热量分布饼图"
            :data="mealData"
            :colors="
              mealData.length === 6
                ? ['#97a1f0', '#6096e6', '#6cb9c7', '#eb94c6', '#f7be7d', '#64bfa7']
                : ['#97a1f0', '#6cb9c7', '#eb94c6', '#f7be7d', '#64bfa7']
            "
            chart-id="meal-chart"
          />
        </view>
      </view>
    </view>
    <view class="menu-list mt-10">
      <view v-for="item in menuDetails" :key="item.id" class="menu-list-item">
        <view class="menu-list-item-top flex justify-between">
          <view class="meal-plan-value">{{ item.mealPlanValue }}</view>
        </view>
        <view class="menu-list-item-bottom">
          <view class="flex justify-between">
            <view style="flex: 1">
              <view v-for="(detail, index) in item.details" :key="index" class="menu-item">
                <view class="menu-name">{{ detail.name }}</view>
                <view class="menu-ingredients">{{ detail.ingredients }}</view>
              </view>
            </view>

            <view style="flex: 0 0 30px" @click="handleCollect(item)">
              <van-icon v-if="!item.isCollect" name="like-o" />
              <van-icon v-else name="like" color="#ef4444" />
            </view>
          </view>
          <!-- <view class="ingredients" style="font-size: 13px">{{ item.ingredients }}</view> -->
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import {
  getDayEnergyRatio,
  getDayNutrientRatio,
  getMenuList,
  modifyBrowseTime,
  modifyCollect
} from '@/api/index'
import PieChart from '@/components/PieChart.vue'
import useUserPageStayTime from '@/hooks/useUserPageStayTime.js'
import { useUserStore } from '@/store'
import Taro, { redirectTo } from '@tarojs/taro'
import { computed, onMounted, ref } from 'vue'

// 展示今日菜谱
const date = ref(new Date().toLocaleDateString().replace(/\//g, '-'))

const menuInfo = ref({
  foodQuantity: 37,
  actualEnergy: 2200,
  recommendQuantity: 2200,
  childrenList: []
})

// 营养素占比数据
const nutritionData = ref([
  { label: '蛋白质', key: 'pctProtein', value: 0, countVal: '' },
  { label: '脂肪', key: 'pctFat', value: 0, countVal: '' },
  { label: '碳水化合物', key: 'pctCHO', value: 0, countVal: '' }
])

// 营养素值数据
const nutritionValueData = ref([
  { label: '蛋白质', key: 'kcalFromProtein', value: 0 },
  { label: '脂肪', key: 'kcalFromFat', value: 0 },
  { label: '碳水化合物', key: 'kcalFromCHO', value: 0 }
])

// 各餐热量分布数据
const mealData = ref([
  { label: '早餐', key: 'breakfast', value: 0 },
  { label: '上午加餐', key: 'morningSnack', value: 0 },
  { label: '午餐', key: 'lunch', value: 0 },
  { label: '下午加餐', key: 'afternoonSnack', value: 0 },
  { label: '晚餐', key: 'dinner', value: 0 },
  { label: '睡前加餐', key: 'midnightSnack', value: 0 }
])

useUserPageStayTime(async (time) => {
  const menu = menuInfo.value
  if (menu) {
    const child = menu.childrenList ? menu.childrenList[0] : null
    if (child) {
      const { dailyMenuId } = child
      const data = {
        dailyMenuId,
        browseTime: time
      }
      await modifyBrowseTime(data)
    }
  }
})

// 获取今日菜谱
const getMenuListData = async () => {
  Taro.showLoading('加载中...')
  const data = {
    id: useUserStore().userInfo.id,
    recordDate: date.value,
    pageNum: 1,
    pageSize: 10
  }
  const res = await getMenuList(data)
  if (res.Content.length) {
    menuInfo.value = res.Content[0]
  }
  console.log(menuInfo)
  Taro.hideLoading()
}

// 食谱详细信息
const menuDetails = computed(() => {
  const list = menuInfo.value.childrenList
  list.forEach((item) => {
    const { menuName, ingredients } = item
    const menuNameArr = menuName.split('、')
    const ingredientArr = ingredients.split('、')
    const details = []
    menuNameArr.forEach((name, index) => {
      details.push({
        name,
        ingredients: ingredientArr[index]
      })
    })
    item.details = details
  })
  return list
})

// 收藏菜谱
const handleCollect = async (item) => {
  console.log('item>>', item)
  const data = {
    dailyMenuId: item.dailyMenuId,
    isCollect: item.isCollect ? 0 : 1,
    mealType: item.mealPlanValue
  }
  const res = await modifyCollect(data)
  if (res.Head.Code === 1) {
    Taro.showToast({ title: item.isCollect ? '已取消' : '已收藏', icon: 'none' })
    item.isCollect = !item.isCollect
  } else {
    Taro.showToast({ title: '操作失败', icon: 'error' })
  }
}

const getEnergyRatio = async () => {
  const data = {
    womanId: useUserStore().userInfo.id,
    recordDate: date.value
  }
  const res = await getDayEnergyRatio(data)
  if (res.Head.Ret === 1) {
    const resData = res.Content[0]
    mealData.value.forEach((item) => {
      const { key } = item
      const ratio = resData[key]
      item.value = Number(ratio.slice(0, -1))
    })
    mealData.value = mealData.value.filter((item) => item.value)
  }
}

const getNutrientRatio = async () => {
  const data = {
    womanId: useUserStore().userInfo.id,
    recordDate: date.value
  }
  const res = await getDayNutrientRatio(data)
  if (res.Head.Ret === 1) {
    const resData = res.Content[0]
    nutritionData.value.forEach((item) => {
      const { key } = item
      const ratio = resData[key]
      item.value = Number(ratio.slice(0, -1))
    })
    nutritionValueData.value.forEach((item) => {
      const { key } = item
      const value = resData[key]
      item.value = value
    })
  }
}

onMounted(async () => {
  if (!useUserStore().userInfo.id) {
    redirectTo({ url: '/pages/login/index' })
  } else {
    await getMenuListData()
    getEnergyRatio()
    getNutrientRatio()
  }
})
</script>

<style lang="scss" scoped>
.menu-header {
  padding: 10px;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  border-radius: 10px;
}

.charts-container {
  margin-top: 10px;

  .charts-row {
    display: flex;
    gap: 10px;

    .chart-item {
      flex: 1;
    }
  }
}

.menu-list {
  padding: 10px;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  border-radius: 10px;

  .menu-list-item {
    padding: 20px 10px;
    border-bottom: 1px solid #f0f0f0;

    .meal-plan-value {
      color: #ff4d76;
      // font-size: 16px !important;
      font-weight: bold;
      font-size: 28px;
      margin-bottom: 10px;
    }

    .menu-item {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      .menu-name {
        font-size: 24px;
        font-weight: bold;
      }

      .menu-ingredients {
        font-size: 22px;
      }
    }
  }
}
</style>
