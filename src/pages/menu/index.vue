<template>
  <van-nav-bar title="今日菜谱" fixed />
  <view class="container">
    <view class="menu-header flex-between">
      <view class="menu-header-left">
        <view class="bold">{{ menuInfo.recommendQuantity }}Kcal</view>
        <view style="padding-top: 10px; color: #9a9fa9; font-size: 16px">每日建议摄入能量</view>
      </view>
      <view class="menu-header-right" style="color: #9a9fa9; font-size: 16px">
        今日食材
        <span style="color: green">{{ menuInfo.foodQuantity }}</span>
        种
      </view>
    </view>
    <view class="menu-list mt-10">
      <view v-for="item in menuInfo?.childrenList" :key="item.id" class="menu-list-item">
        <view class="menu-list-item-top flex justify-between">
          <view class="meal-plan-value" style="font-size: 16px">{{ item.mealPlanValue }}</view>
          <view style="font-size: 16px">{{ item.date }}</view>
        </view>
        <view class="menu-list-item-bottom">
          <view class="flex justify-between">
            <view class="menu-name" style="font-size: 14px">{{ item.menuName }}</view>
            <view @click="handleCollect(item)">
              <van-icon v-if="!item.isCollect" name="like-o" />
              <van-icon v-else name="like" color="#ef4444" />
            </view>
          </view>
          <view class="ingredients" style="font-size: 13px">{{ item.ingredients }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { getMenuList, modifyBrowseTime, modifyCollect } from '@/api/index'
import useUserPageStayTime from '@/hooks/useUserPageStayTime.js'
import { useUserStore } from '@/store'
import Taro, { redirectTo, useDidShow } from '@tarojs/taro'
import { onMounted, ref } from 'vue'

// 展示今日菜谱
const date = ref(new Date().toLocaleDateString().replace(/\//g, '-'))

const menuInfo = ref({
  foodQuantity: 12,
  actualEnergy: 0,
  recommendQuantity: 0,
  childrenList: []
})

useUserPageStayTime(async (time) => {
  const menu = menuInfo.value
  if (menu) {
    const child = menu.childrenList ? menu.childrenList[0] : null
    if (child) {
      const { dailyMenuId } = child
      const data = {
        dailyMenuId,
        browseTime: time
      }
      await modifyBrowseTime(data)
    }
  }
})

useDidShow(() => {
  // setTime()
  getMenuListData()
})

// 获取今日菜谱
const getMenuListData = async () => {
  Taro.showLoading('加载中...')
  const data = {
    id: useUserStore().userInfo.id,
    recordDate: date.value,
    pageNum: 1,
    pageSize: 10
  }
  const res = await getMenuList(data)
  if (res.Content.length) {
    menuInfo.value = res.Content[0]
  }
  console.log(menuInfo)
  Taro.hideLoading()
}

// 收藏菜谱
const handleCollect = async (item) => {
  console.log('item>>', item)
  const data = {
    dailyMenuId: item.dailyMenuId,
    isCollect: item.isCollect ? 0 : 1,
    mealType: item.mealPlanValue
  }
  const res = await modifyCollect(data)
  if (res.Head.Code === 1) {
    Taro.showToast({ title: item.isCollect ? '已取消' : '已收藏', icon: 'none' })
    item.isCollect = !item.isCollect
  } else {
    Taro.showToast({ title: '操作失败', icon: 'error' })
  }
}
onMounted(() => {
  if (!useUserStore().userInfo.id) {
    redirectTo({ url: '/pages/login/index' })
  }
})
</script>

<style lang="scss" scoped>
.menu-header {
  padding: 10px;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  border-radius: 10px;
}

.menu-list {
  padding: 10px;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  border-radius: 10px;

  .menu-list-item {
    padding: 20px 10px;
    border-bottom: 1px solid #f0f0f0;

    .meal-plan-value {
      color: #ff4d76;
      // font-size: 16px !important;
      font-weight: bold;
    }

    .menu-name {
      font-size: 14px;
      font-weight: bold;
      padding: 10px 0;
    }

    .ingredients {
      font-size: 13px;
    }
  }
}
</style>
