<!-- 登录 -->
<template>
  <div class="login-container">
    <div class="avatar flex-center">
      <i class="ri-user-pregnant-line"><van-icon name="contact-o" size="40" /></i>
    </div>
    <button @click="getWechatPhone">微信一键登录</button>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import wx from 'weixin-js-sdk'
import { initWechatSDK, wechatAuthRedirect } from '@/utils/wechat-auth'

const phoneInfo = ref(null)

// 初始化微信 SDK
onMounted(async () => {
  await initWechatSDK()
  wx.ready(() => {
    console.log('微信SDK配置成功')
  })
})

// 发起微信登录
const getWechatPhone = () => {
  wx.ready(() => {
    wx.checkJsApi({
      jsApiList: ['getWechatUserInfo'],
      success: () => {
        // 跳转授权页获取 code
        wechatAuthRedirect()
      }
    })
  })
}

// 处理 URL 中的授权 code
const handleAuthCode = async () => {
  const urlParams = new URLSearchParams(window.location.search)
  const code = urlParams.get('code')

  if (code) {
    const res = await fetch('https://your-api.com/wechat/get-phone', {
      method: 'POST',
      body: JSON.stringify({ code })
    })
    phoneInfo.value = await res.json()
    console.log('解密后的手机号:', phoneInfo.value.phoneNumber)
  }
}

// 页面加载时检查 code
// handleAuthCode()
</script>

<style lang="scss" scoped>
.login-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  .avatar {
    margin-bottom: 60px;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    color: #fff;
    background-color: #f5c0cc;
  }

  button {
    width: 80%;
    border-radius: 4px;
    background-color: #ff85a2;
    color: #fff;
    border-radius: 20px;
  }
}
</style>
