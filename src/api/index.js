import request from '@/utils/request'
// const request = service.create({
//   baseURL: 'http://10.0.0.87:8299/'
// })

// export default request

// 登录
export const login = (data) =>
  request({
    url: '/food/gravida/register',
    method: 'post',
    data
  })

// 获取推荐菜谱
export const getMenuList = (data) =>
  request({
    url: '/food/gravida/recommend',
    method: 'post',
    data
  })
// 获取食材类型
export const getFoodType = (data) =>
  request({
    url: '/food/select/foodType',
    method: 'post',
    data
  })

// 获取食材列表
export const getFoodList = (data) =>
  request({
    url: '/food/select/food',
    method: 'post',
    data
  })

// 获取体重记录
export const getWeightRecord = (data) =>
  request({
    url: '/food/gravida/queryWeightLog',
    method: 'post',
    data
  })

// 保存血糖信息
export const saveBloodSugarInfo = (data) =>
  request({
    url: '/food/gravida/saveBloodSugar',
    method: 'post',
    data
  })

// 收藏食谱
export const modifyCollect = (data) =>
  request({
    url: 'food/gravida/modifyCollect',
    method: 'post',
    data
  })
// 报告食谱页停留时间
export const modifyBrowseTime = (data) =>
  request({
    url: 'food/gravida/modifyBrowseTime',
    method: 'post',
    data
  })
