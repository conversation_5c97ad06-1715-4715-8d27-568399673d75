import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

export const useUserStore = defineStore(
  'user',
  () => {
    // 用户唯一标识码
    const userId = ref('张三')
    // 用户信息
    const userInfo = ref({
      id: '', // 用户id
      name: '', // 姓名
      phone: '', // 手机号
      heightCm: '', // 身高
      bloodGlucoseRecord: [], // 血糖记录
      prePregnancyWeightKg: '', // 孕前体重
      currentWeightKg: '', // 当前体重
      pregnancyWeeks: '', // 孕周
      pregnancyDays: '', // 孕天
      mealPlan: '0', // 餐食计划
      tastePreference: '0', // 口味偏好
      stapleFoodPreference: '0', // 主食偏好
      dairyPreference: '0', // 乳制品偏好
      meatPreference: '0', // 肉类偏好
      foodAllergy: [], // 食物过敏
      otherAllergy: '' // 其他过敏
    })

    // 孕期
    const pregnancyType = computed(() => {
      const { pregnancyWeeks } = userInfo.value
      if (pregnancyWeeks < 14) {
        return '妊娠早期'
      }
      if (pregnancyWeeks >= 28) {
        return '妊娠晚期'
      }
      return '妊娠中期'
    })

    // 设置用户信息
    const setUserInfo = (info) => {
      userInfo.value = { ...userInfo.value, ...info }
    }

    // 获取用户信息
    const getUserInfo = () => {
      return userInfo.value
    }
    return { userInfo, setUserInfo, getUserInfo, pregnancyType }
  },
  { persist: true }
)
