import { useDidHide, useDidShow, useUnload } from '@tarojs/taro'
import { ref } from 'vue'
export default function useUserPageStayTime(callback) {
  const startTime = ref(0)
  // 最短有效停留时间（4秒）
  const MIN_STAY_DURATION = 4000

  useDidShow(() => {
    startTime.value = Date.now()
  })

  const getDurationTime = () => {
    const currentTime = Date.now()
    const stayDuration = currentTime - startTime.value
    return stayDuration
  }
  // 页面卸载时立即上报
  useUnload(() => {
    report(getDurationTime())
  })

  useDidHide(() => {
    console.log('onHide')
    const stayDuration = getDurationTime()
    // 停留时间过短不上报
    if (stayDuration < MIN_STAY_DURATION) return

    report(stayDuration)
  })

  const report = (time) => {
    console.log('上报')
    if (callback) {
      callback(time)
    }
  }
}
