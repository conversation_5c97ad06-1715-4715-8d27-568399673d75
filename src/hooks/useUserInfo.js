import { getUserInfo } from '@/api/user'
import { useUserStore } from '@/store'
import { redirectTo } from '@tarojs/taro'
import { storeToRefs } from 'pinia'
import useSWRV from 'swrv'
import { computed, onMounted, watch } from 'vue'

export function useUserInfo() {
  const userStore = useUserStore()
  const user = storeToRefs(userStore)
  const userInfo = computed(() => {
    return user.userInfo.value || {}
  })
  const userId = userInfo.value.id
  const params = {
    id: userId,
    pageNum: 1,
    pageSize: 10
  }
  const { data, isLoading, mutate } = useSWRV(
    userId ? { type: 'userInfo', params } : null,
    async (data) => {
      const result = await getUserInfo(data.params)
      return result
    }
  )

  watch(
    [() => data.value?.Content, () => isLoading.value],
    ([content, loading]) => {
      // 数据加载完成且有内容时更新store
      if (content && !loading) {
        userStore.setUserInfo(content)
      }
    },
    { immediate: true } // 初始化时立即执行一次
  )

  onMounted(() => {
    if (!userId) {
      redirectTo({ url: '/pages/login/index' })
    }
  })

  return { userInfo, mutate }
}
