import { getUserInfo } from '@/api/user'
import { useUserStore } from '@/store'
import { reLaunch } from '@tarojs/taro'
import { storeToRefs } from 'pinia'
import { computed, ref, watch } from 'vue'

export function useUserInfo() {
  const userStore = useUserStore()
  const user = storeToRefs(userStore)
  const userInfo = computed(() => {
    return user.userInfo.value || {}
  })
  const userId = userInfo.value.id
  const params = {
    id: userId,
    pageNum: 1,
    pageSize: 10
  }

  const data = ref()
  const isLoading = ref(false)
  const fetchData = async () => {
    isLoading.value = true
    const result = await getUserInfo(params)
    data.value = result
    isLoading.value = false
  }

  const mutate = fetchData

  watch(
    [() => data.value?.Content, () => isLoading.value],
    ([content, loading]) => {
      // 数据加载完成且有内容时更新store
      if (content && !loading) {
        userStore.setUserInfo(content)
      }
    },
    { immediate: true } // 初始化时立即执行一次
  )

  console.log('getUserInfo', userId)
  if (!userId) {
    reLaunch({ url: '/pages/login/index' })
  } else {
    fetchData()
  }

  return { userInfo, mutate }
}
