.step-indicator {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 10px 0;
  position: relative;
}

.step-indicator::before {
  content: '';
  position: absolute;
  left: 10%;
  right: 10%;
  top: 50%;
  height: 2px;
  background: #e0e0e0;
  z-index: 1;
}
.step {
  width: 55px;
  height: 55px;
  border-radius: 50%;
  display: flex !important;
  align-items: center;
  justify-content: center;
  background: white;
  border: 2px solid #e0e0e0;
  color: #999;
  font-weight: bold;
  position: relative;
  z-index: 2;
}

.step.active {
  background: #e5457a;
  border-color: #e5457a;
  color: white;
}

.step-line {
  flex-grow: 1;
  height: 2px;
  background: #e0e0e0;
  margin: 0 10px;
}

.step-line.active {
  background: #e5457a;
}

.action-buttons {
  display: flex;
  gap: 15px;
  margin-top: 30px;
}

.action-buttons button {
  flex: 1;
}

.option-label {
  display: block;
  margin-bottom: 8px;
  font-size: 16px;
  font-weight: 500;
}

.option-label .required::after {
  content: ' *';
  color: #e5457a;
}

.option-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin-top: 10px;
}

.option-grid.col-3 {
  grid-template-columns: repeat(3, 1fr);
}

.option-btn {
  font-size: 32px;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: white;
  text-align: center;
  cursor: pointer;
}

.option-btn.selected {
  border-color: #e5457a;
  color: #e5457a;
  background: rgba(229, 69, 122, 0.05);
}

.btn-outline {
  width: 100%;
  background: white;
  color: #e5457a;
  border: 1px solid #e5457a;
  border-radius: 14px;
  padding: 12px;
  font-size: 28px;
  cursor: pointer;
  margin-top: 48px;
}

.section-subtitle {
  color: #666;
  font-size: 14px;
  margin-bottom: 10px;
}

.input-group .btn-verify {
  background: #e5457a;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0 15px;
  margin-left: 10px;
  font-size: 14px;
  cursor: pointer;
  white-space: nowrap;
}

.input-group .btn-toggle-pwd {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  width: 24px;
  height: 24px;
  opacity: 0.5;
}

.checkbox-container {
  display: flex;
  align-items: center;
  margin-top: 20px;
}

.checkbox-container input {
  margin-right: 8px;
}
