import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate' // 持久化插件

import './app.scss'

import Vant from 'vant'
import 'vant/lib/index.css'

const app = createApp({
  onShow(options) {
    console.log('App onShow.')
  }
  // 入口组件不需要实现 render 方法，即使实现了也会被 taro 所覆盖
})

const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)
app.use(Vant)
app.use(pinia) // 使用pinia
export default app
