import wx from 'weixin-js-sdk'

// 初始化微信配置
export const initWechatSDK = async () => {
  wx.config({
    debug: true, // 开启调试
    appId: 'wx228e6f7a5f7592cf',
    timestamp: Date.now(),
    nonceStr: 'YOUR_NONCESTR', // 随机字符串
    signature: 'YOUR_SIGNATURE', // 签名
    jsApiList: ['login', 'getUserInfo'] // 配置需要调用的 JS API
  })
}

// 触发微信网页授权
export const wechatAuthRedirect = () => {
  const redirectUri = encodeURIComponent(window.location.href)
  window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx228e6f7a5f7592cf&redirect_uri=${redirectUri}&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect`
}
