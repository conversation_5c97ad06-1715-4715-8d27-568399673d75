/* eslint-disable */
import Taro from '@tarojs/taro'
import axios from 'axios'
import Cookies from 'js-cookie'

const resError = {
  Head: {
    Ret: -1,
    Msg: '网络错误，请重试'
  }
}
// 当接口方法为get时,将data中的数据拼在地址上
function calcUrl(url, { Keys }) {
  if (Keys === undefined) {
    return url
  }
  let keyList = Object.keys(Keys)
  let suffix = '?'
  if (keyList.length === 0) {
    return url
  } else {
    keyList.map((item) => {
      suffix += `${item}=${Keys[item]}&`
    })
    return url + suffix.slice(0, suffix.length - 1)
  }
}
// import store from '../store'
// import { getToken } from '@/utils/auth'
axios.defaults.baseURL = process.env.TARO_APP_ENV === 'development' ? '' : '/'
// 请求超时时间
axios.defaults.timeout = 30000
// request拦截器
let serverLoading = null
let requestNum = 0
axios.interceptors.request.use(
  (config) => {
    requestNum++
    // serverLoading = Loading.service({
    //   lock: true,
    //   target: 'body',
    //   text: '加载资源中',
    //   spinner: 'el-icon-loading',
    //   background: 'rgba(244,244,244,0.1)',
    //   customClass: 'requestIp_customClass'
    // })
    // if (store.getters.token) {
    // config.headers['Authorization'] = getToken() // 让每个请求携带自定义token 请根据实际情况自行修改
    // }
    if (Cookies.get('HospId') !== undefined) {
      config.headers['HospId'] = Cookies.get('HospId')
      config.headers['UserName'] = encodeURI(Cookies.get('UserName'))
      config.headers['WorkerId'] = Cookies.get('WorkerId')
      config.headers['DeptId'] = Cookies.get('DeptId')
    }
    return config
  },
  (error) => {
    console.log('error', error)
    setTimeout(() => {
      if (serverLoading) {
        requestNum = 0
        // serverLoading.close()
      }
    }, 300)
    // Do something with request error
    return Promise.reject(resError)
  }
)

// respone拦截器
axios.interceptors.response.use(
  (response) => {
    requestNum--
    if (requestNum <= 1) {
      setTimeout(() => {
        requestNum = 0
        // serverLoading.close()
      }, 100)
    }
    return response
  },
  (error) => {
    setTimeout(() => {
      if (serverLoading) {
        requestNum = 0
        // serverLoading.close()
      }
    }, 300)
    return Promise.reject(resError)
  }
)

function checkCode(res) {
  res = res ? res : resError
  switch (res.Head.Ret) {
    case 0:
      Taro.showToast({
        title: res.Head.Msg,
        icon: 'error',
        duration: 5 * 1000
      })
      return res
    case 1:
      // Taro.showToast({
      //   title: res.Head.Msg,
      //   icon: 'success',
      //   duration: 5 * 1000
      // })
      return res
    case -1:
      Taro.showToast({
        title: res.Head.Msg,
        icon: 'error',
        duration: 5 * 1000
      })
      return res
    case -2:
      Taro.showToast({
        title: res.Head.Msg,
        icon: 'error',
        duration: 5 * 1000
      })
      return res
    default:
      Taro.showToast({
        title: res.Head.Msg,
        icon: 'error',
        duration: 5 * 1000
      })
      return
  }
}
function checkCodeByUpload(res) {
  res = res ? res : { code: -1 }
  switch (res.code) {
    case 0:
      return res
    default:
      Taro.showToast({
        title: '上传失败',
        icon: 'error',
        duration: 5 * 1000
      })
      return
  }
}
function checkStatusError(response) {
  return Promise.resolve(response)
}
function checkCodeFalse(res) {
  res = res ? res : resError
  switch (res.Head.Ret) {
    case 0:
      Taro.showToast({
        title: res.Head.Msg,
        icon: 'error',
        duration: 5 * 1000
      })
      return res
    case 1:
      // Taro.showToast({
      //   title: res.Head.Msg,
      //   icon: 'success',
      //   duration: 5 * 1000
      // })
      return res
    case -1:
      Taro.showToast({
        title: res.Head.Msg,
        icon: 'error',
        duration: 5 * 1000
      })
      return res
    case -2:
      Taro.showToast({
        title: res.Head.Msg,
        icon: 'error',
        duration: 5 * 1000
      })
      return res
    default:
      Taro.showToast({
        title: res.Head.Msg,
        icon: 'error',
        duration: 5 * 1000
      })
      return
  }
}

function checkStatus(response) {
  if (response.status === 200 || response.status === 304) {
    return response.data
  }
  Taro.showToast({
    title: '网络错误，请重试！',
    icon: 'error',
    duration: 5 * 1000
  })
  return {
    data: {
      status: -404,
      message: response.statusText,
      data: response.statusText
    }
  }
}
const request = function ({
  url,
  method,
  data,
  contentType,
  isShowMsg = 'true',
  serverType = 'java'
}) {
  if (method === 'post') {
    let withData = null

    if (serverType === 'java') {
      // 兼容文件上传
      if (contentType) {
        withData = data
      } else if (data === undefined) {
        withData = {
          Keys: {}
        }
      } else if (!data.Keys) {
        withData = {
          Keys: data
        }
      } else {
        withData = data
      }
    } else {
      withData = data
    }

    if (contentType) {
      return axios
        .post(url, withData, contentType)
        .then(checkStatus, checkStatusError)
        .then(checkCodeByUpload)
    }
    if (isShowMsg) {
      return axios
        .post(url, withData, contentType)
        .then(checkStatus, checkStatusError)
        .then(checkCode)
    } else {
      return axios
        .post(url, withData, contentType)
        .then(checkStatus, checkStatusError)
        .then(checkCodeFalse)
    }
  } else {
    const queryUrl = calcUrl(url, data)
    if (isShowMsg) {
      return axios.get(queryUrl).then(checkStatus, checkStatusError).then(checkCode)
    } else {
      return axios.get(queryUrl).then(checkStatus, checkStatusError).then(checkCodeFalse)
    }
  }
}
export default request
