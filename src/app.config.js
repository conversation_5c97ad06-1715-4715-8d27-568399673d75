export default defineAppConfig({
  pages: [
    'pages/login/index',
    'pages/home/<USER>',
    'pages/home/<USER>',
    'pages/profile/index',
    'pages/menu/index',
    'pages/profile/notice',
    'pages/profile/history-menu',
    'pages/profile/edit-profile',
    'pages/userInfo/step1',
    'pages/userInfo/step2',
    'pages/userInfo/step3'
  ],
  window: {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: 'WeChat',
    navigationBarTextStyle: 'black'
  },
  tabBar: {
    custom: false,
    color: '#000000',
    selectedColor: '#DC143C',
    backgroundColor: '#ffffff',
    list: [
      {
        pagePath: 'pages/home/<USER>',
        selectedIconPath: 'assets/tabBar/home-selected.svg',
        iconPath: 'assets/tabBar/home-unselected.svg',
        text: '膳食画像'
      },
      {
        pagePath: 'pages/menu/index',
        selectedIconPath: 'assets/tabBar/menu-selected.svg',
        iconPath: 'assets/tabBar/menu-unselected.svg',
        text: '今日菜谱'
      },
      {
        pagePath: 'pages/profile/index',
        selectedIconPath: 'assets/tabBar/mine-selected.svg',
        iconPath: 'assets/tabBar/mine-unselected.svg',
        text: '个人中心'
      }
    ]
  }
})
