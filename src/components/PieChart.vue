<template>
  <view class="pie-chart-container">
    <view class="chart-title">{{ title }}</view>
    <view class="chart-wrapper">
      <canvas :id="chartId" ref="chartCanvas" class="chart-canvas"></canvas>
      <view class="legend">
        <view v-for="(item, index) in chartData" :key="index" class="legend-item">
          <view class="legend-color" :style="{ backgroundColor: colors[index] }" />
          <view class="legend-text">{{ item.label }} {{ item.percentage }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import Taro from '@tarojs/taro'
import { Chart, registerables } from 'chart.js'
import { computed, nextTick, onUnmounted, ref, toRaw, watch } from 'vue'

// 注册Chart.js组件
Chart.register(...registerables)

const props = defineProps({
  title: {
    type: String,
    default: '饼图'
  },
  data: {
    type: Array,
    default: () => []
  },
  colors: {
    type: Array,
    default: () => ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']
  },
  chartId: {
    type: String,
    required: true
  },
  valueData: {
    type: Array,
    default: () => []
  }
})

const chartCanvas = ref(null)
const chartInstance = ref(null)

// 计算图表数据
const chartData = computed(() => {
  const showValue = props.valueData.length
  const data = props.valueData.length ? props.valueData : props.data
  return data.map((item, index) => {
    const percentage = showValue ? item.value : item.value + '%'
    return {
      ...item,
      percentage,
      color: props.colors[index % props.colors.length]
    }
  })
})

// 创建饼图
const createChart = () => {
  return new Promise((resolve, reject) => {
    nextTick(() => {
      const query = Taro.createSelectorQuery()
      query.exec(async () => {
        const cxt = Taro.createCanvasContext(props.chartId, this)
        chartInstance.value = new Chart(cxt, {
          type: 'pie',
          data: {
            labels: [],
            datasets: [
              {
                data: [],
                backgroundColor: [],
                borderWidth: 2,
                borderColor: '#fff'
              }
            ]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: false // 隐藏默认图例，自定义显示
              },
              tooltip: {
                callbacks: {
                  label: function (context) {
                    const label = context.label || ''
                    const value = context.parsed
                    return `${label}: ${value}%`
                  }
                }
              }
            }
          }
        })
        resolve()
      })
    })
  })
}

// 更新图表数据
const updateChart = async (newData) => {
  if (!chartInstance.value) {
    await createChart()
  }

  if (newData.length > 0 && chartInstance.value) {
    const data = toRaw(newData)
    // 更新数据
    chartInstance.value.data.labels = data.map((item) => item.label)
    chartInstance.value.data.datasets[0].data = data.map((item) => item.value)
    chartInstance.value.data.datasets[0].backgroundColor = props.colors.slice(0, data.length)
    chartInstance.value.update('active')
  }
}

// 监听数据变化，重新创建图表
watch(
  () => props.data,
  async (newData) => {
    await updateChart(newData)
  },
  { deep: true, immediate: true }
)

onUnmounted(() => {
  if (chartInstance.value) {
    // 在销毁前先停止所有动画
    chartInstance.value.stop()
    chartInstance.value.destroy()
    chartInstance.value = null
    chartCanvas.value = null
  }
})
</script>

<style lang="scss" scoped>
.pie-chart-container {
  background: #fff;
  border-radius: 12px;
  padding: 16px 16px 0 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 10px 0;
  height: 100%;

  .chart-title {
    font-size: 26px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 16px;
    color: #333;
  }

  .chart-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;

    .chart-canvas {
      width: 240px !important;
      height: 240px !important;
      margin-bottom: 16px;
    }

    .legend {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: center;

      .legend-item {
        display: flex;
        align-items: center;
        gap: 6px;
        margin-right: 18px;
        margin-bottom: 10px;

        .legend-color {
          width: 12px;
          height: 12px;
          border-radius: 2px;
        }

        .legend-text {
          font-size: 18px;
          color: #464545;
        }
      }
    }
  }
}
</style>
