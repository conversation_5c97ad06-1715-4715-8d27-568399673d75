<template>
  <view class="pie-chart-container">
    <view class="chart-title">{{ title }}</view>
    <view class="chart-wrapper">
      <view class="pie-chart" :style="getPieChartStyle()">
        <view class="pie-center" />
      </view>
      <view class="legend">
        <view v-for="(item, index) in chartData" :key="index" class="legend-item">
          <view class="legend-color" :style="{ backgroundColor: colors[index] }" />
          <view class="legend-text">{{ item.label }} {{ item.percentage }}%</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  title: {
    type: String,
    default: '饼图'
  },
  data: {
    type: Array,
    default: () => []
  },
  colors: {
    type: Array,
    default: () => ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']
  },
  chartId: {
    type: String,
    required: true
  }
})

// 计算图表数据
const chartData = computed(() => {
  const total = props.data.reduce((sum, item) => sum + item.value, 0)
  let currentAngle = 0

  return props.data.map((item, index) => {
    const percentage = ((item.value / total) * 100).toFixed(1)
    const angle = (item.value / total) * 360
    const startAngle = currentAngle
    currentAngle += angle

    return {
      ...item,
      percentage,
      angle,
      startAngle,
      color: props.colors[index % props.colors.length]
    }
  })
})

// 获取饼图样式
const getPieChartStyle = () => {
  if (chartData.value.length === 0) return {}

  let gradientStops = []
  let currentAngle = 0

  chartData.value.forEach((item) => {
    const endAngle = currentAngle + item.angle
    gradientStops.push(`${item.color} ${currentAngle}deg ${endAngle}deg`)
    currentAngle = endAngle
  })

  return {
    background: `conic-gradient(${gradientStops.join(', ')})`
  }
}
</script>

<style lang="scss" scoped>
.pie-chart-container {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 10px 0;

  .chart-title {
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 16px;
    color: #333;
  }

  .chart-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;

    .pie-chart {
      position: relative;
      width: 120px;
      height: 120px;
      border-radius: 50%;
      overflow: hidden;
      margin-bottom: 16px;

      .pie-center {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 40px;
        height: 40px;
        background: #fff;
        border-radius: 50%;
      }
    }

    .legend {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .legend-item {
        display: flex;
        align-items: center;
        gap: 8px;

        .legend-color {
          width: 12px;
          height: 12px;
          border-radius: 2px;
        }

        .legend-text {
          font-size: 14px;
          color: #666;
        }
      }
    }
  }
}
</style>
