<template>
  <view class="pie-chart-container">
    <view class="chart-title">{{ title }}</view>
    <view class="chart-wrapper">
      <!-- 使用纯CSS实现的饼图 -->
      <view class="pie-chart" :style="getPieChartStyle()">
        <view class="pie-center" />
      </view>
      <view class="legend">
        <view v-for="(item, index) in chartData" :key="index" class="legend-item">
          <view class="legend-color" :style="{ backgroundColor: colors[index] }" />
          <view class="legend-text">{{ item.label }} {{ item.percentage }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  title: {
    type: String,
    default: '饼图'
  },
  data: {
    type: Array,
    default: () => []
  },
  colors: {
    type: Array,
    default: () => ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']
  },
  chartId: {
    type: String,
    required: true
  },
  valueData: {
    type: Array,
    default: () => []
  }
})

// 计算图表数据
const chartData = computed(() => {
  const showValue = props.valueData.length
  const data = props.valueData.length ? props.valueData : props.data
  return data.map((item, index) => {
    const percentage = showValue ? item.value : item.value + '%'
    return {
      ...item,
      percentage,
      color: props.colors[index % props.colors.length]
    }
  })
})

// 获取饼图样式
const getPieChartStyle = () => {
  if (chartData.value.length === 0) return {}

  let gradientStops = []
  let currentAngle = 0

  chartData.value.forEach((item) => {
    const value = props.valueData.length ? item.value : parseFloat(item.value)
    const angle = (value / 100) * 360
    const endAngle = currentAngle + angle
    gradientStops.push(`${item.color} ${currentAngle}deg ${endAngle}deg`)
    currentAngle = endAngle
  })

  return {
    background: `conic-gradient(${gradientStops.join(', ')})`
  }
}
</script>

<style lang="scss" scoped>
.pie-chart-container {
  background: #fff;
  border-radius: 12px;
  padding: 16px 16px 0 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 10px 0;
  height: 100%;

  .chart-title {
    font-size: 26px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 16px;
    color: #333;
  }

  .chart-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;

    .pie-chart {
      width: 240px;
      height: 240px;
      border-radius: 50%;
      margin-bottom: 16px;
      position: relative;

      .pie-center {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 80px;
        height: 80px;
        background: #fff;
        border-radius: 50%;
      }
    }

    .legend {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: center;

      .legend-item {
        display: flex;
        align-items: center;
        gap: 6px;
        margin-right: 18px;
        margin-bottom: 10px;

        .legend-color {
          width: 12px;
          height: 12px;
          border-radius: 2px;
        }

        .legend-text {
          font-size: 18px;
          color: #464545;
        }
      }
    }
  }
}
</style>
