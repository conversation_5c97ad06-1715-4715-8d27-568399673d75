<template>
  <view class="pie-chart-container">
    <view class="chart-title">{{ title }}</view>
    <view class="chart-wrapper">
      <canvas :id="chartId" class="chart-canvas"></canvas>
    </view>
  </view>
</template>

<script setup>
import { useReady } from '@tarojs/taro'
import { Chart, registerables } from 'chart.js'
import { nextTick, ref, watch } from 'vue'

// 注册Chart.js组件
Chart.register(...registerables)

const props = defineProps({
  title: {
    type: String,
    default: '饼图'
  },
  data: {
    type: Array,
    default: () => []
  },
  colors: {
    type: Array,
    default: () => ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']
  },
  chartId: {
    type: String,
    required: true
  }
})

const chartInstance = ref(null)

const createChart = async () => {
  await nextTick()

  const canvas = Taro.createCanvasContext(props.chartId, this)
  if (!canvas) {
    console.error(`Canvas with id ${props.chartId} not found`)
    return
  }

  // 销毁之前的图表实例
  if (chartInstance.value) {
    chartInstance.value.destroy()
  }
  console.log('🚀 >>>> props.data>>>>', props.data)

  chartInstance.value = new Chart(canvas, {
    type: 'pie',
    data: {
      labels: props.data.map((item) => item.label),
      datasets: [
        {
          data: props.data.map((item) => item.value),
          backgroundColor: props.colors.slice(0, props.data.length),
          borderWidth: 2,
          borderColor: '#fff'
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false // 隐藏默认图例，我们自定义显示
        },
        tooltip: {
          callbacks: {
            label: function (context) {
              const label = context.label || ''
              const value = context.parsed
              const total = context.dataset.data.reduce((a, b) => a + b, 0)
              const percentage = ((value / total) * 100).toFixed(1)
              return `${label}: ${percentage}%`
            }
          }
        }
      },
      layout: {
        padding: 10
      }
    }
  })
}

// 监听数据变化，重新创建图表
watch(
  () => props.data,
  () => {
    if (props.data.length > 0) {
      createChart()
    }
  },
  { deep: true }
)

useReady(() => {
  if (props.data.length > 0) {
    createChart()
  }
})
</script>

<style lang="scss" scoped>
.pie-chart-container {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 10px 0;

  .chart-title {
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 16px;
    color: #333;
  }

  .chart-wrapper {
    position: relative;
    height: 200px;
    width: 100%;

    .chart-canvas {
      width: 100% !important;
      height: 100% !important;
    }
  }
}
</style>
