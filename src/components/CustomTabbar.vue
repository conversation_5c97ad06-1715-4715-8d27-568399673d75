<template>
  <div class="custom-tabbar">
    <div
      v-for="(item, index) in tabList"
      :key="index"
      class="tabbar-item"
      @click="switchTab(item.pagePath)"
    >
      <img
        :src="currentPath === item.pagePath ? item.selectedIconPath : item.iconPath"
        class="tabbar-icon"
      />
      <div :class="['tabbar-text', currentPath === item.pagePath ? 'active' : '']">
        {{ item.text }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import Taro from '@tarojs/taro'

const currentPath = ref('')

const tabList = [
  {
    pagePath: '/pages/home/<USER>',
    text: '首页',
    iconPath: '/assets/logo.png',
    selectedIconPath: '/assets/logo.png'
  },
  {
    pagePath: '/pages/menu/index',
    text: '菜单',
    iconPath: '/assets/icons/record.png',
    selectedIconPath: '/assets/icons/record-active.png'
  },
  {
    pagePath: '/pages/profile/index',
    text: '我的',
    iconPath: '',
    selectedIconPath: ''
  }
]

const switchTab = (url) => {
  Taro.switchTab({
    url
  })
}

onMounted(() => {
  const pages = Taro.getCurrentPages()
  const currentPage = pages[pages.length - 1]
  currentPath.value = '/' + currentPage.route
})
</script>

<style lang="scss" scoped>
.custom-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100px;
  background: #fff;
  display: flex;
  padding-bottom: env(safe-area-inset-bottom);
  box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.1);

  .tabbar-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    font-weight: 600;
    color: #666;

    .tabbar-icon {
      width: 24px;
      height: 24px;
      margin-bottom: 2px;
    }

    .tabbar-text {
      &.active {
        color: #ff4d76;
      }
    }
  }
}
</style>
