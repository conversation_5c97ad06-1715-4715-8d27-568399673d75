@use './styles/variables' as *;
@use './styles/userInfo.scss' as *;

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

body {
  background-color: $primary-light;
  color: #333;
}

.text-center {
  text-align: center;
}
.p10 {
  padding: 10px;
}

.pt10 {
  padding-top: 10px;
}

.ml-10 {
  margin-left: 10px;
}

.mt-10 {
  margin-top: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}

.mt-20 {
  margin-top: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

.flex {
  display: flex;
}

.justify-between {
  justify-content: space-between;
}

.bold {
  font-weight: bold;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.container {
  padding: 110px 20px 50px 20px;
  background: #f5f5f5;
  min-height: 100vh;
  position: relative;
}

.page {
  display: none;
  padding: 20px;
  min-height: 100vh;
}

.active {
  display: block;
}

.form-group {
  margin-top: 10px;
  margin-bottom: 40px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #333;
  font-size: 32px;
}

.form-group .required::after {
  content: ' *';
  color: #e5457a;
}

input[type='text'],
input[type='password'],
input[type='number'],
input[type='tel'] {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 16px;
  color: #333;
}

input:focus {
  outline: none;
  border-color: #e5457a;
}

.input-group {
  display: flex;
  position: relative;
}

.input-group input {
  flex-grow: 1;
}

.input-group .unit {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  font-size: 28px;
}

.btn-primary {
  width: 100%;
  background: #e5457a;
  color: white;
  border: none;
  border-radius: 14px;
  padding: 12px;
  font-size: 28px;
  cursor: pointer;
  margin-top: 48px;
}

.link {
  color: #e5457a;
  text-decoration: none;
  font-size: 14px;
}
.weui-tabbar__icon {
  width: 40px !important;
  height: 40px !important;
}
.weui-tabbar__label {
  font-size: 24px !important;
}
:root {
  --van-nav-bar-icon-color: #000 !important;
}

/* 恢复浏览器原生样式 */
input {
  padding: 20px;
  border: 1px solid #dfd5d5 !important;
  border-radius: 12px;
  font-family: inherit;
  font-size: 28px;
  background-color: #fff;
}
