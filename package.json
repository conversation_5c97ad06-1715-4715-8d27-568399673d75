{"name": "DRS", "version": "1.0.0", "private": true, "description": "膳食推荐系统移动端", "templateInfo": {"name": "default", "typescript": false, "css": "Sass", "framework": "Vue3"}, "scripts": {"build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "npx taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:harmony-hybrid": "taro build --type harmony-hybrid", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:harmony-hybrid": "npm run build:harmony-hybrid -- --watch", "lint": "eslint . --ext .js,.vue --ignore-path .eslintignore --cache --cache-location node_modules/.cache/.eslintcache", "lint:fix": "eslint . --ext .js,.vue --fix --ignore-path .eslint<PERSON>ore", "format:all": "prettier --write --ignore-path .gitignore \"**/*.{js,vue,json,html,md,css,scss}\"", "lint-staged": "lint-staged", "prepare": "husky"}, "lint-staged": {"*.{js,vue}": ["prettier --write", "eslint --fix"], "*.{json,html,md,css,scss}": ["prettier --write"]}, "browserslist": {"development": ["defaults and fully supports es6-module", "maintained node versions"], "production": ["last 3 versions", "Android >= 4.1", "ios >= 8"]}, "author": "", "dependencies": {"@babel/runtime": "^7.24.4", "@tarojs/components": "4.0.12", "@tarojs/helper": "4.0.12", "@tarojs/plugin-framework-vue3": "4.0.12", "@tarojs/plugin-platform-alipay": "4.0.12", "@tarojs/plugin-platform-h5": "4.0.12", "@tarojs/plugin-platform-harmony-hybrid": "4.0.12", "@tarojs/plugin-platform-jd": "4.0.12", "@tarojs/plugin-platform-qq": "4.0.12", "@tarojs/plugin-platform-swan": "4.0.12", "@tarojs/plugin-platform-tt": "4.0.12", "@tarojs/plugin-platform-weapp": "4.0.12", "@tarojs/runtime": "4.0.12", "@tarojs/shared": "4.0.12", "@tarojs/taro": "4.0.12", "chart.js": "^4.4.9", "js-cookie": "^3.0.5", "path": "^0.12.7", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.3.0", "swrv": "^1.1.0", "vant": "^4.9.19", "vue": "^3.0.0", "vue-chartjs": "^5.3.2", "vue-router": "^4.5.1", "weixin-js-sdk": "^1.6.5"}, "devDependencies": {"@babel/core": "^7.24.4", "@babel/plugin-transform-class-properties": "7.25.9", "@tarojs/cli": "4.0.12", "@tarojs/plugin-framework-react": "4.0.12", "@tarojs/taro-loader": "4.0.12", "@tarojs/webpack5-runner": "4.0.12", "@types/node": "^18", "@types/webpack-env": "^1.13.6", "@vue/babel-plugin-jsx": "^1.2.2", "@vue/compiler-sfc": "^3.0.0", "babel-plugin-import": "^1.13.8", "babel-plugin-module-resolver": "^5.0.2", "babel-preset-taro": "4.0.12", "css-loader": "^7.1.1", "eslint": "^8.57.0", "eslint-config-prettier": "^10.1.5", "eslint-config-taro": "4.0.12", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-vue": "^8.7.1", "husky": "^9.1.7", "lint-staged": "^16.1.2", "postcss": "^8.4.38", "prettier": "^3.6.2", "sass": "^1.75.0", "style-loader": "^3.3.4", "stylelint": "^16.4.0", "vue-loader": "^17.4.2", "webpack": "5.91.0"}}